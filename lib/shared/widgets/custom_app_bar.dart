import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

/// A custom app bar widget that can be reused across the app.
///
/// This app bar includes a back button and a title, with customizable styling.
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// The title to display in the app bar.
  final String title;

  /// Callback function when the back button is pressed.
  /// If null, Navigator.pop will be used as the default behavior.
  final VoidCallback? onBackPressed;

  /// Whether to center the title. Defaults to false.
  final bool centerTitle;

  /// The spacing between the leading widget and the title. Defaults to 8.
  final double titleSpacing;

  /// Additional actions to display at the end of the app bar.
  final List<Widget>? actions;

  /// The background color of the app bar. Defaults to white.
  final Color backgroundColor;

  /// The elevation of the app bar. Defaults to 0.
  final double elevation;

  /// The system overlay style to apply. If null, a default style will be used.
  final SystemUiOverlayStyle? systemOverlayStyle;

  final Color titleColor;

  /// Widget to display at the bottom of the AppBar
  final PreferredSizeWidget? bottom;

  /// Whether to show the back button. Defaults to true.
  final bool showBackButton;

  /// Creates a custom app bar.
  const CustomAppBar({
    super.key,
    required this.title,
    this.onBackPressed,
    this.centerTitle = false,
    this.titleSpacing = 0,
    this.actions,
    this.backgroundColor = Colors.white,
    this.elevation = 4,
    this.systemOverlayStyle,
    this.titleColor = Colors.black,
    this.bottom,
    this.showBackButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return AppBar(
      backgroundColor: backgroundColor,
      elevation: 0,
      centerTitle: centerTitle,
      systemOverlayStyle: systemOverlayStyle ??
          const SystemUiOverlayStyle(
            statusBarColor: Colors.white,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
          ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showBackButton) ...[
            GestureDetector(
              onTap: onBackPressed ??
                  () {
                    Navigator.pop(context);
                  },
              child: Padding(
                padding: const EdgeInsets.fromLTRB(
                  8,
                  8,
                  4,
                  8,
                ),
                child: Icon(
                  Icons.arrow_back_ios_new,
                  color: titleColor,
                  size: 20,
                ),
              ),
            ),
          ],
          Padding(
            padding: EdgeInsets.only(left: showBackButton ? 0 : 16),
            child: Text(
              title,
              style: textTheme.montserratTitleExtraSmall
                  .copyWith(color: titleColor),
            ),
          ),
        ],
      ),
      titleSpacing: 0,
      automaticallyImplyLeading: false,
      actions: actions,
      bottom: bottom != null
          ? PreferredSize(
              preferredSize: Size.fromHeight(bottom!.preferredSize.height + 1),
              child: Column(
                children: [
                  bottom!,
                  Container(
                    height: 1,
                    color: AppColors.appBarBorderBlack,
                  ),
                ],
              ),
            )
          : PreferredSize(
              preferredSize: const Size.fromHeight(1),
              child: Container(
                height: 0.5,
                color: AppColors.appBarBorderBlack,
              ),
            ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
      kToolbarHeight + (bottom?.preferredSize.height ?? 0.0) + 0.5);
}
