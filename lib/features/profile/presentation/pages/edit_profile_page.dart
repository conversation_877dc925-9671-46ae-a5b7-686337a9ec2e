import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import '../blocs/edit_profile/edit_profile_cubit.dart';
import '../../data/models/profile_response.dart';
import '../../data/models/update_profile_request.dart';

@RoutePage()
class EditProfilePage extends StatelessWidget {
  final ProfileResponse? profileData;

  const EditProfilePage({
    super.key,
    this.profileData,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<EditProfileCubit>(),
      child: _EditProfileView(profileData: profileData),
    );
  }
}

class _EditProfileView extends StatefulWidget {
  final ProfileResponse? profileData;

  const _EditProfileView({this.profileData});

  @override
  State<_EditProfileView> createState() => _EditProfileViewState();
}

class _EditProfileViewState extends State<_EditProfileView> {
  // Form controllers
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _suburbController = TextEditingController();
  final TextEditingController _postcodeController = TextEditingController();
  final TextEditingController _deliveryController = TextEditingController();
  final TextEditingController _postalAddressController =
      TextEditingController();
  final TextEditingController _postalSuburbController = TextEditingController();
  final TextEditingController _postalPostcodeController =
      TextEditingController();

  // Dropdown values
  String? _selectedCountry;
  String? _selectedState;
  String? _selectedPostalCountry;
  String? _selectedPostalState;

  // Checkbox state
  bool _postalSameAsHome = false;

  // Profile data
  ProfileResponse? _profileData;

  // Form key
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    // If profile data is passed, use it directly, otherwise load from API
    if (widget.profileData != null) {
      _prefillFormData(widget.profileData!);
    } else {
      _loadProfileData();
    }
  }

  /// Loads the current user's profile data from storage and triggers profile loading
  Future<void> _loadProfileData() async {
    final dataManager = sl<DataManager>();
    final token = await dataManager.getAuthToken();
    final userId = await dataManager.getUserId();

    if (token != null && userId != null) {
      if (mounted) {
        context.read<EditProfileCubit>().loadProfile(
              token: token,
              userId: userId,
            );
      }
    }
  }

  /// Pre-fills form fields with existing profile data
  void _prefillFormData(ProfileResponse profileResponse) {
    final data = profileResponse.data;
    if (data != null) {
      setState(() {
        _profileData = profileResponse;

        // Prefill basic information
        _mobileController.text = data.mobile ?? '';
        _addressController.text = data.address ?? '';
        _suburbController.text = data.suburb ?? '';
        _postcodeController.text = data.postcode ?? '';
        _deliveryController.text = data.pDeliveryComment ?? '';

        // Prefill postal address
        _postalAddressController.text = data.pAddress ?? '';
        _postalSuburbController.text = data.pSuburb ?? '';
        _postalPostcodeController.text = data.pPostcode ?? '';

        // Set dropdown selections
        _selectedCountry = data.country;
        _selectedState = data.state;
        _selectedPostalCountry = data.pCountry;
        _selectedPostalState = data.pRegion;
      });
    }
  }

  /// Handles the "postal same as home" checkbox toggle
  void _handlePostalSameAsHomeToggle(bool? value) {
    setState(() {
      _postalSameAsHome = value ?? false;

      if (_postalSameAsHome) {
        // Copy home address to postal address
        _postalAddressController.text = _addressController.text;
        _postalSuburbController.text = _suburbController.text;
        _postalPostcodeController.text = _postcodeController.text;
        _selectedPostalCountry = _selectedCountry;
        _selectedPostalState = _selectedState;
      } else {
        // Clear postal address fields
        _postalAddressController.clear();
        _postalSuburbController.clear();
        _postalPostcodeController.clear();
        _selectedPostalCountry = null;
        _selectedPostalState = null;
      }
    });
  }

  /// Saves the profile changes
  Future<void> _saveProfileChanges() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token == null || userId == null) {
          SnackBarService.error(
            context: context,
            message: 'Authentication error. Please login again.',
          );
          return;
        }

        // Helper function to get country ID from country name
        String getCountryId(String? country) {
          switch (country) {
            case 'Australia':
              return '14';
            case 'New Zealand':
              return '13';
            default:
              return '14'; // Default to Australia
          }
        }

        // Helper function to get state ID from state name
        String getStateId(String? state) {
          switch (state) {
            case 'NSW':
              return '4';
            case 'VIC':
              return '5';
            case 'QLD':
              return '6';
            case 'SA':
              return '7';
            case 'WA':
              return '8';
            case 'TAS':
              return '9';
            case 'NT':
              return '10';
            case 'ACT':
              return '11';
            default:
              return '4'; // Default to NSW
          }
        }

        final request = UpdateProfileRequest(
          userId: userId,
          token: token,
          address: _addressController.text.trim(),
          countryId: getCountryId(_selectedCountry),
          country: _selectedCountry ?? 'Australia',
          stateId: getStateId(_selectedState),
          state: _selectedState ?? 'NSW',
          suburb: _suburbController.text.trim(),
          postcode: _postcodeController.text.trim(),
          pAddress: _postalAddressController.text.trim(),
          pCountryId: getCountryId(_selectedPostalCountry),
          pCountry: _selectedPostalCountry ?? 'Australia',
          pSuburb: _postalSuburbController.text.trim(),
          pPostcode: _postalPostcodeController.text.trim(),
          pRegion: _selectedPostalState ?? 'NSW',
          pDeliveryComment: _deliveryController.text.trim(),
          mobile: _mobileController.text.trim(),
          deviceLatitude: 1.0, // Default values as per the example
          deviceLongitude: 1.0,
        );

        if (mounted) {
          context.read<EditProfileCubit>().updateProfile(request: request);
        }
      } catch (e) {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Error preparing profile update: ${e.toString()}',
          );
        }
      }
    }
  }

  @override
  void dispose() {
    // Dispose all controllers to prevent memory leaks
    _mobileController.dispose();
    _addressController.dispose();
    _suburbController.dispose();
    _postcodeController.dispose();
    _deliveryController.dispose();
    _postalAddressController.dispose();
    _postalSuburbController.dispose();
    _postalPostcodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Edit Profile',
      ),
      body: BlocListener<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state is EditProfileLoaded) {
            _prefillFormData(state.profileResponse);
          } else if (state is EditProfileUpdateSuccess) {
            SnackBarService.success(
              context: context,
              message: 'Profile updated successfully!',
            );
            // Optionally navigate back or refresh the data
            _loadProfileData(); // Reload to get updated data
          } else if (state is EditProfileError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
        },
        child: BlocBuilder<EditProfileCubit, EditProfileState>(
          builder: (context, state) {
            if (state is EditProfileLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  color: AppColors.primaryBlue,
                ),
              );
            }

            return Stack(
              children: [
                Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        // Single unified container
                        _buildUnifiedProfileContainer(),
                        const Gap(24),

                        // Save button
                        _buildSaveButton(),
                        const Gap(24),
                      ],
                    ),
                  ),
                ),

                // Loading overlay when updating
                if (state is EditProfileUpdating)
                  Container(
                    color: Colors.black.withOpacity(0.3),
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.primaryBlue,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// Builds the unified container with all profile information
  Widget _buildUnifiedProfileContainer() {
    final textTheme = Theme.of(context).textTheme;
    final data = _profileData?.data;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header section with square image
          _buildProfileHeader(textTheme, data),

          // Divider
          Container(
            height: 1,
            margin: const EdgeInsets.symmetric(vertical: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.blackTint2.withOpacity(0.1),
                  AppColors.blackTint2,
                  AppColors.blackTint2.withOpacity(0.1),
                ],
              ),
            ),
          ),

          // Personal Information Section
          _buildSectionTitle('Personal Information'),
          const Gap(16),
          _buildPersonalInfoFields(data),

          // Address Information Section
          const Gap(16),
          _buildSectionTitle('Address Information'),
          const Gap(20),
          _buildAddressInfoFields(),

          // Delivery Instructions Section
          const Gap(16),
          _buildSectionTitle('Delivery Instructions'),
          const Gap(20),
          _buildDeliveryInstructionsField(),

          // Postal Address Section
          const Gap(16),
          _buildSectionTitle('Postal Address'),
          const Gap(20),
          _buildPostalAddressFields(),
        ],
      ),
    );
  }

  /// Builds the profile header with square image
  Widget _buildProfileHeader(TextTheme textTheme, dynamic data) {
    return Column(
      children: [
        // Square profile image
        Align(
          alignment: Alignment.center,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primaryBlue.withOpacity(0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: data?.profileImageUrl?.isNotEmpty == true
                  ? Image.network(
                      data!.profileImageUrl!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultSquareAvatar();
                      },
                    )
                  : _buildDefaultSquareAvatar(),
            ),
          ),
        ),
        const Gap(20),
        Text(
          '${data?.firstName ?? 'John'} ${data?.lastName ?? 'Doe'}',
          style: textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
            fontSize: 22,
            color: AppColors.black,
            height: 1.2,
          ),
        ),
        const Gap(6),
        Text(
          data?.email ?? '<EMAIL>',
          style: textTheme.bodyMedium?.copyWith(
            color: AppColors.blackTint1,
            fontSize: 15,
            fontWeight: FontWeight.w500,
          ),
        ),
        const Gap(10),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            'Employee ID   : ${data?.contractorId?.toString() ?? '123456789'}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.primaryBlue,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
          ),
        ),
        // Profile info
        // Expanded(
        //   child: Column(
        //     crossAxisAlignment: CrossAxisAlignment.start,
        //     children: [

        //       const Gap(4),
        //       Container(
        //         padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        //         decoration: BoxDecoration(
        //           color: AppColors.primaryBlue.withOpacity(0.1),
        //           borderRadius: BorderRadius.circular(6),
        //         ),
        //         child:
        //       ),
        //     ],
        //   ),
        // ),
      ],
    );
  }

  /// Builds the default square avatar
  Widget _buildDefaultSquareAvatar() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: AppColors.lightGrey2,
        borderRadius: BorderRadius.circular(10),
      ),
      child: const Icon(
        Icons.person_rounded,
        size: 40,
        color: AppColors.blackTint1,
      ),
    );
  }

  /// Builds a section title with modern styling
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w700,
            fontSize: 18,
            color: AppColors.black,
            letterSpacing: -0.5,
          ),
    );
  }

  /// Builds personal information fields
  Widget _buildPersonalInfoFields(dynamic data) {
    return Column(
      children: [
        _buildModernTextField(
          label: 'Mobile Number',
          controller: _mobileController,
          keyboardType: TextInputType.phone,
          hintText: 'Enter your mobile number',
          prefixIcon: Icons.phone_outlined,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Mobile number is required';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Builds address information fields
  Widget _buildAddressInfoFields() {
    return Column(
      children: [
        _buildModernTextField(
          label: 'Address',
          controller: _addressController,
          hintText: 'Enter your street address',
          prefixIcon: Icons.location_on_outlined,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Address is required';
            }
            return null;
          },
        ),
        const Gap(20),

        // Country and State row
        Row(
          children: [
            Expanded(
              child: _buildModernDropdownField(
                label: 'Country',
                value: _selectedCountry,
                items: ['Australia', 'New Zealand', 'United States'],
                onChanged: (value) => setState(() => _selectedCountry = value),
                prefixIcon: Icons.public_outlined,
                validator: (value) {
                  if (value == null) {
                    return 'Country is required';
                  }
                  return null;
                },
              ),
            ),
            const Gap(16),
            Expanded(
              child: _buildModernDropdownField(
                label: 'State',
                value: _selectedState,
                items: ['NSW', 'VIC', 'QLD', 'SA', 'WA', 'TAS', 'NT', 'ACT'],
                onChanged: (value) => setState(() => _selectedState = value),
                prefixIcon: Icons.map_outlined,
                validator: (value) {
                  if (value == null) {
                    return 'State is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const Gap(20),

        // Suburb and Postcode row
        Row(
          children: [
            Expanded(
              child: _buildModernTextField(
                label: 'Suburb',
                controller: _suburbController,
                hintText: 'Enter suburb',
                prefixIcon: Icons.home_work_outlined,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Suburb is required';
                  }
                  return null;
                },
              ),
            ),
            const Gap(16),
            Expanded(
              child: _buildModernTextField(
                label: 'Postcode',
                controller: _postcodeController,
                keyboardType: TextInputType.number,
                hintText: 'Enter postcode',
                prefixIcon: Icons.local_post_office_outlined,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Postcode is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds delivery instructions field
  Widget _buildDeliveryInstructionsField() {
    return _buildModernTextField(
      label: 'Special Instructions',
      controller: _deliveryController,
      hintText: 'Enter any special delivery instructions',
      prefixIcon: Icons.note_add_outlined,
      maxLines: 3,
    );
  }

  /// Builds postal address fields
  Widget _buildPostalAddressFields() {
    return Column(
      children: [
        // Modern checkbox
        _buildModernCheckbox(
          title: 'Postal Address Same As Home Address',
          value: _postalSameAsHome,
          onChanged: _handlePostalSameAsHomeToggle,
        ),

        // Postal address fields (show only if not same as home)
        if (!_postalSameAsHome) ...[
          const Gap(24),
          _buildModernTextField(
            label: 'Postal Address',
            controller: _postalAddressController,
            hintText: 'Enter postal address',
            prefixIcon: Icons.markunread_mailbox_outlined,
          ),
          const Gap(20),

          // Postal Country and State row
          Row(
            children: [
              Expanded(
                child: _buildModernDropdownField(
                  label: 'Postal Country',
                  value: _selectedPostalCountry,
                  items: ['Australia', 'New Zealand', 'United States'],
                  onChanged: (value) =>
                      setState(() => _selectedPostalCountry = value),
                  prefixIcon: Icons.public_outlined,
                ),
              ),
              const Gap(16),
              Expanded(
                child: _buildModernDropdownField(
                  label: 'Postal State',
                  value: _selectedPostalState,
                  items: ['NSW', 'VIC', 'QLD', 'SA', 'WA', 'TAS', 'NT', 'ACT'],
                  onChanged: (value) =>
                      setState(() => _selectedPostalState = value),
                  prefixIcon: Icons.map_outlined,
                ),
              ),
            ],
          ),
          const Gap(20),

          // Postal Suburb and Postcode row
          Row(
            children: [
              Expanded(
                child: _buildModernTextField(
                  label: 'Postal Suburb',
                  controller: _postalSuburbController,
                  hintText: 'Enter postal suburb',
                  prefixIcon: Icons.home_work_outlined,
                ),
              ),
              const Gap(16),
              Expanded(
                child: _buildModernTextField(
                  label: 'Postal Postcode',
                  controller: _postalPostcodeController,
                  keyboardType: TextInputType.number,
                  hintText: 'Enter postal postcode',
                  prefixIcon: Icons.local_post_office_outlined,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Builds the save button with modern design
  Widget _buildSaveButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            AppColors.primaryBlue,
            AppColors.primaryBlue.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _saveProfileChanges,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.save_outlined,
              size: 20,
            ),
            const Gap(8),
            Text(
              'Save Changes',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.white,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a modern text field with enhanced styling
  Widget _buildModernTextField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    String? hintText,
    IconData? prefixIcon,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            color: AppColors.black,
            letterSpacing: 0.1,
          ),
        ),
        const Gap(8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hintText,
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppColors.primaryBlue.withOpacity(0.7),
                    size: 20,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppColors.blackTint2.withOpacity(0.5),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppColors.blackTint2.withOpacity(0.5),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: AppColors.primaryBlue,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            filled: true,
            fillColor: AppColors.lightGrey1,
            contentPadding: EdgeInsets.symmetric(
              horizontal: prefixIcon != null ? 12.0 : 16.0,
              vertical: maxLines > 1 ? 16.0 : 18.0,
            ),
            hintStyle: textTheme.bodyMedium?.copyWith(
              color: AppColors.blackTint1.withOpacity(0.8),
              fontSize: 15,
            ),
          ),
          style: textTheme.bodyMedium?.copyWith(
            color: AppColors.black,
            fontSize: 15,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Builds a modern dropdown field with enhanced styling
  Widget _buildModernDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required void Function(String?) onChanged,
    IconData? prefixIcon,
    String? Function(String?)? validator,
  }) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            color: AppColors.black,
            letterSpacing: 0.1,
          ),
        ),
        const Gap(8),
        DropdownButtonFormField<String>(
          value: value,
          validator: validator,
          decoration: InputDecoration(
            hintText: 'Select...',
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppColors.primaryBlue.withOpacity(0.7),
                    size: 20,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppColors.blackTint2.withOpacity(0.5),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppColors.blackTint2.withOpacity(0.5),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: AppColors.primaryBlue,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            filled: true,
            fillColor: AppColors.lightGrey1,
            contentPadding: EdgeInsets.symmetric(
              horizontal: prefixIcon != null ? 12.0 : 16.0,
              vertical: 18.0,
            ),
          ),
          isExpanded: true,
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(
                item,
                style: textTheme.bodyMedium?.copyWith(
                  color: AppColors.black,
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
          onChanged: onChanged,
          icon: Icon(
            Icons.keyboard_arrow_down_rounded,
            color: AppColors.primaryBlue.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  /// Builds a modern checkbox widget
  Widget _buildModernCheckbox({
    required String title,
    required bool value,
    required void Function(bool?) onChanged,
  }) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: value
            ? AppColors.primaryBlue.withOpacity(0.05)
            : AppColors.lightGrey1,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value
              ? AppColors.primaryBlue.withOpacity(0.3)
              : AppColors.blackTint2.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: GestureDetector(
        onTap: () => onChanged(!value),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: value ? AppColors.primaryBlue : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: value ? AppColors.primaryBlue : AppColors.blackTint2,
                  width: 2,
                ),
              ),
              child: value
                  ? const Icon(
                      Icons.check_rounded,
                      size: 16,
                      color: Colors.white,
                    )
                  : null,
            ),
            const Gap(16),
            Expanded(
              child: Text(
                title,
                style: textTheme.bodyMedium?.copyWith(
                  color: AppColors.black,
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
