import 'package:storetrack_app/core/network/network_info.dart';
import 'package:storetrack_app/features/home/<USER>/models/availability_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/history_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/induction_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/leave_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/skills_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/useful_links_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_contact_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart';

import '../../../../shared/models/result.dart';
import '../../data/datasources/home_local_datasource.dart';
import '../../data/datasources/home_remote_datasource.dart';
import '../entities/tasks_request_entity.dart';
import '../entities/tasks_response_entity.dart';
import '../../data/repositories/home_repository.dart';
import '../entities/calendar_response_entity.dart';
import '../usecases/get_calendar_usecase.dart';
import '../entities/submit_report_request_entity.dart';
import '../entities/submit_report_response_entity.dart';
import '../entities/previous_tasks_response_entity.dart';

class HomeRepositoryImpl implements HomeRepository {
  final HomeRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;
  final HomeLocalDataSource localDataSource;

  HomeRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
    required this.localDataSource,
  });

  @override
  Future<Result<TasksResponseEntity>> getTasks(
      TasksRequestEntity request) async {
    final localData = await localDataSource.getTasks();
    if (localData != null) {
      return Result.success(localData);
    } else {
      if (await networkInfo.isConnected) {
        // Online: fetch from remote and cache locally
        var result = await remoteDataSource.getTasks(request);
        if (result.isSuccess && result.data != null) {
          await localDataSource.saveTasks(result.data!);
        }
        return result;
      } else {
        return Result.failure(
            'No internet connection and no cached data available.');
      }
    }
  }

  @override
  Future<Result<TaskDetail>> getTaskDetail(int taskId) async {
    // Always fetch from local database since tasks are cached locally
    final taskDetail = await localDataSource.getTaskDetail(taskId);
    if (taskDetail != null) {
      return Result.success(taskDetail);
    } else {
      return Result.failure(
          'Task with ID $taskId not found in local database.');
    }
  }

  @override
  Future<Result<CalendarResponseEntity>> getCalendarData(
      GetCalendarParams request) async {
    if (await networkInfo.isConnected) {
      // Online: fetch from remote and cache locally
      var result = await remoteDataSource.getCalendarData(request);
      if (result.isSuccess && result.data != null) {
        await localDataSource.saveCalendarInfo(result.data!);
      }
      return result;
    } else {
      // Offline: try to get from local cache
      final localData = await localDataSource.getCalendarInfo();
      if (localData != null) {
        return Result.success(localData);
      } else {
        return Result.failure(
            'No internet connection and no cached calendar data available.');
      }
    }
  }

  @override
  Future<Result<SubmitReportResponseEntity>> submitReport(
      SubmitReportRequestEntity request) async {
    return remoteDataSource.submitReport(request);
  }

  @override
  Future<Result<PreviousTasksResponseEntity>> getPreviousTasks(
      PreviousTasksRequestEntity request) async {
    if (await networkInfo.isConnected) {
      // Online: fetch from remote
      return await remoteDataSource.getPreviousTasks(request);
    } else {
      // Offline: return failure
      return Result.failure(
          'No internet connection. Previous tasks require an active connection.');
    }
  }

  @override
  Future<Result<PreviousTasksResponseEntity>> getPreviousTasksOptimize(
      PreviousTasksRequestEntity request) async {
    if (await networkInfo.isConnected) {
      // Online: fetch from remote
      return await remoteDataSource.getPreviousTasksOptimize(request);
    } else {
      // Offline: return failure
      return Result.failure(
          'No internet connection. Optimized previous tasks require an active connection.');
    }
  }

  @override
  Future<Result<UsefulLinksResponse>> getUsefulLinks({
    required String token,
    required String userId,
  }) async {
    return await remoteDataSource.getUsefulLinks(
      token: token,
      userId: userId,
    );
  }

  @override
  Future<Result<InductionResponse>> getInduction({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getInduction(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<HistoryResponse>> getHistory({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getHistory(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<SkillsResponse>> getSkills({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getSkills(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<AvailabilityResponse>> getAvailability({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getAvailability(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveInduction({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> inductions,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveInduction(
        token: token,
        userId: userId,
        inductions: inductions,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveSkills({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> skills,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveSkills(
        token: token,
        userId: userId,
        skills: skills,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveAvailability({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> days,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveAvailability(
        token: token,
        userId: userId,
        days: days,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<LeaveResponse>> getLeave({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getLeave(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> deleteLeave({
    required String token,
    required String userId,
    required List<int> leaveIds,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.deleteLeave(
        token: token,
        userId: userId,
        leaveIds: leaveIds,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<StoreContactResponse>> getStoreContacts({
    required String token,
    required String userId,
    required String storeId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getStoreContacts(
        token: token,
        userId: userId,
        storeId: storeId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveStoreContact({
    required StoreContactRequest request,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveStoreContact(
        request: request,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<StoreContactTypesResponse>> getStoreContactTypes({
    required String token,
    required String userId,
    required String storeId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getStoreContactTypes(
        token: token,
        userId: userId,
        storeId: storeId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<StoreCommentsResponse>> getStoreComments({
    required String taskId,
    required String userId,
    required String token,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getStoreComments(
        taskId: taskId,
        userId: userId,
        token: token,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveStoreComment({
    required StoreCommentRequest request,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveStoreComment(
        request: request,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<String>> getAutoScheduleLink({
    required String token,
    required String userId,
    int dayOffset = 0,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getAutoScheduleLink(
        token: token,
        userId: userId,
        dayOffset: dayOffset,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }
}
