import 'package:flutter/material.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';

class AllTasksList extends StatelessWidget {
  final List<schedule.TaskDetail> allApiTasks;
  final bool isCheckboxMode;
  final bool areAllItemsSelected;
  final Function(List<TaskDetail>) onSelectionChanged;
  final TaskDetail Function(schedule.TaskDetail) convertScheduleToDatum;

  const AllTasksList({
    super.key,
    required this.allApiTasks,
    required this.isCheckboxMode,
    required this.areAllItemsSelected,
    required this.onSelectionChanged,
    required this.convertScheduleToDatum,
  });

  @override
  Widget build(BuildContext context) {
    // For the "All" tab, show all scheduled tasks without date filtering
    List<TaskDetail> tasksToShow = [];

    // Get all confirmed tasks that are not open
    tasksToShow = allApiTasks
        .where((task) =>
            task.taskStatus == "Confirmed" &&
            task.isOpen == false &&
            task.scheduledTimeStamp != null)
        .toList()
      // Sort by scheduled date and time
      ..sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));

    if (tasksToShow.isEmpty) {
      return const EmptyState(message: 'No scheduled tasks available');
    }

    // For menu button, show the regular list with all tasks
    return ReorderableStoreList(
      tasks: tasksToShow,
      isCalendarMode: isCheckboxMode,
      showScheduledDate: true, // Show date in All tab
      showTickIndicator: true,
      showAllDisclosureIndicator: false,
      permanentlyDisableAllDisclosureIndicator: false,
      isOpenTask: true,
      onSelectionChanged: onSelectionChanged,
      selectAll: areAllItemsSelected,
    );
  }
}
