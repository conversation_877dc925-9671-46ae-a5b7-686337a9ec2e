import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/data/models/login_response.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/dashboard/dashboard_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/dashboard/dashboard_state.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';

import '../../../../config/routes/app_router.gr.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/storage/data_manager.dart';
import '../widgets/dashboard_item.dart';

@RoutePage()
class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  final String actualDeviceUid = "8b7a6774c878a206";
  late String actualUserId;
  final String actualAppVersion = "9.9.9";
  final List<String> actualTasksToUnschedule = [];
  late String actualUserToken;

  // Task count variables
  int countUnscheduled = 0;
  int countScheduled = 0;
  int countPos = 0;
  int countCompleted = 0;
  int countToday = 0;
  double completionPercentage = 0.0;

  bool? isPriceCheckerUser;
  bool? isAdminUniversal;
  bool? dayCheckIn;
  bool? pos;
  bool? openTasks;
  bool? vacancies;
  bool? premAutoSchedule;
  bool? premAvailability;
  bool? premAutoSchedule4Weeks;
  bool? watermarkImages;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      precacheImage(const AssetImage(AppAssets.bgImage), context);
    });
    _setTiles();
    _initializeData();
  }

  Future<void> _setTiles() async {
    LoginResponse? loginResponse = await sl<DataManager>().getLoginResponse();
    if (loginResponse != null) {
      setState(() {
        isPriceCheckerUser = loginResponse.data?.isPriceCheckerUser;
        isAdminUniversal = loginResponse.data?.isAdminUniversal;
        dayCheckIn = loginResponse.data?.dayCheckIn;
        pos = loginResponse.data?.pos;
        openTasks = loginResponse.data?.openTasks;
        vacancies = loginResponse.data?.vacancies;
        premAutoSchedule = loginResponse.data?.premAutoSchedule;
        premAvailability = loginResponse.data?.premAvailability;
        premAutoSchedule4Weeks = loginResponse.data?.premAutoSchedule4Weeks;
        watermarkImages = loginResponse.data?.watermarkImages;
      });
    }
  }

  Future<void> _initializeData() async {
    try {
      // Get user ID and token from DataManager
      actualUserId = await sl<DataManager>().getUserId() ?? "0";
      actualUserToken = await sl<DataManager>().getAuthToken() ?? "0";

      // Fetch unscheduled tasks
      if (mounted) {
        context.read<DashboardCubit>().fetchDashboardData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize dashboard: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _refreshDashboardData() async {
    try {
      // Fetch dashboard data again
      if (mounted) {
        await context.read<DashboardCubit>().fetchDashboardData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
      return;
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to refresh dashboard: ${e.toString()}',
        );
      }
      // Re-throw the error to indicate the refresh failed
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return BlocConsumer<DashboardCubit, DashboardState>(
      listener: (context, state) {
        if (state is DashboardLoaded) {
          setState(() {
            countUnscheduled = state.countUnscheduled;
            countScheduled = state.countScheduled;
            countPos = state.countPos;
            countCompleted = state.countCompleted;
            countToday = state.countToday;

            // Calculate completion percentage
            final int totalTasks =
                countUnscheduled + countScheduled + countCompleted;
            if (totalTasks > 0) {
              completionPercentage = countCompleted / totalTasks;
            } else {
              completionPercentage = 0.0;
            }
          });
        } else if (state is DashboardError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          body: Container(
            decoration: const BoxDecoration(
                color: Colors.white,
                image: DecorationImage(
                    image: AssetImage(AppAssets.bgImage), fit: BoxFit.fill)),
            height: MediaQuery.of(context).size.height,
            width: double.infinity,
            child: RefreshIndicator(
              onRefresh: _refreshDashboardData,
              color: AppColors.primaryBlue,
              backgroundColor: Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 32,
                    ),
                    Row(
                      children: [
                        Text(
                          DateFormat('EEEE d MMM')
                              .format(DateTime.now())
                              .toUpperCase(),
                          style: textTheme.montserratParagraphXsmall
                              .copyWith(color: Colors.white),
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: () {
                            context.router.push(const NotificationsRoute());
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(top: 6),
                            child: Image.asset(
                              AppAssets.dashboardNotification,
                              width: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Text(
                      'SUMMARY',
                      style: textTheme.montserratBold
                          .copyWith(color: Colors.white),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    // Show loading indicator or content
                    state is DashboardLoading
                        ? const Expanded(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CircularProgressIndicator(),
                                ],
                              ),
                            ),
                          )
                        : Expanded(
                            child: SingleChildScrollView(
                              physics: const AlwaysScrollableScrollPhysics(),
                              child: Column(
                                children: [
                                  // Row with Cycle Completion and Today
                                  SizedBox(
                                    height: 96,
                                    width: double.infinity,
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: DashboardItem(
                                            title: 'Cycle Completion',
                                            value: '',
                                            progress: completionPercentage,
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 16,
                                        ),
                                        Expanded(
                                          child: DashboardItem(
                                            title: 'Today',
                                            value: countToday.toString(),
                                            icon: Image.asset(
                                              AppAssets.dashboardToday,
                                              width: 28,
                                            ),
                                            ontap: () {
                                              context.navigateTo(
                                                  const TodayRoute());
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // Grid with dashboard items
                                  GridView.count(
                                    padding: const EdgeInsets.only(top: 16),
                                    crossAxisCount: 3, // Fixed crossAxisCount
                                    crossAxisSpacing: 16,
                                    mainAxisSpacing: 16,
                                    childAspectRatio: 1.1,
                                    shrinkWrap:
                                        true, // Added shrinkWrap to fit in Column
                                    physics:
                                        const NeverScrollableScrollPhysics(), // Disable grid scrolling
                                    children: [
                                      if (premAutoSchedule == true)
                                        DashboardItem(
                                          title: 'Auto Schedule',
                                          icon: Image.asset(
                                            AppAssets.autoScheduleIcon,
                                            width: 28,
                                          ),
                                          ontap: () {
                                            context.router.push(
                                                const AutoScheduleRoute());
                                          },
                                        ),
                                      DashboardItem(
                                        title: 'Unscheduled',
                                        value: countUnscheduled.toString(),
                                        ontap: () {
                                          // Use navigateNamedTo instead of push to navigate within the tab structure
                                          // This ensures the bottom navigation bar remains visible
                                          context.navigateTo(
                                              const UnscheduledRoute());
                                        },
                                        icon: Image.asset(
                                          AppAssets.unscheduledIcon,
                                          width: 28,
                                        ),
                                      ),
                                      DashboardItem(
                                        title: 'Scheduled',
                                        value: countScheduled.toString(),
                                        icon: Image.asset(
                                          AppAssets.scheduledIcon,
                                          width: 28,
                                        ),
                                        ontap: () {
                                          context.navigateTo(
                                              const ScheduleRoute());
                                        },
                                      ),
                                      if (pos == true)
                                        DashboardItem(
                                          title: 'POS',
                                          value: countPos.toString(),
                                          icon: Image.asset(
                                            AppAssets.dashboardPos,
                                            width: 20,
                                          ),
                                          ontap: () {
                                            context.navigateTo(
                                                const UnscheduledPosTasksRoute());
                                          },
                                        ),
                                      DashboardItem(
                                        title: 'History',
                                        // value: countCompleted.toString(),
                                        icon: Image.asset(
                                          AppAssets.dashboardHistory,
                                          width: 26,
                                        ),
                                      ),
                                      if (openTasks == true)
                                        DashboardItem(
                                          title: 'Open Tasks',
                                          value: '0',
                                          icon: Image.asset(
                                            AppAssets.dashboardOpenTasks,
                                            width: 28,
                                          ),
                                        ),
                                      if (vacancies == true)
                                        DashboardItem(
                                          title: 'Vacancies',
                                          icon: Image.asset(
                                            AppAssets.homeProfile,
                                            width: 26,
                                          ),
                                        ),
                                      if (isAdminUniversal == true)
                                        DashboardItem(
                                          title: 'Emulate',
                                          icon: Image.asset(
                                            AppAssets.emulateGridImage,
                                            width: 26,
                                            color: AppColors.black,
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
